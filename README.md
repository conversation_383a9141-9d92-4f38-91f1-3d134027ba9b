# Matching Mayhem

A Phaser 3 port of the Matching Mayhem game originally built in Unity.

## Game Description

Matching Mayhem is a fast-paced matching game where players need to identify which of the four options at the bottom matches the image shown at the top. Players earn points based on how quickly they match correctly, with penalties for incorrect matches.

## Game Features

- Countdown timer for the overall game
- Per-round timer that affects scoring
- Score bonuses based on speed
- Penalties for incorrect matches
- Sound effects for different game events
- Mobile-optimized UI
- TicTaps platform integration

## Project Structure

The project follows a modular TypeScript structure:

```
src/
├── assets/        # Game assets (images, sounds, fonts)
├── objects/       # Reusable game objects
│   └── MatchingCard.ts
├── scenes/        # Game scenes
│   ├── PreloadScene.ts
│   ├── GameStartScene.ts
│   ├── GameScene.ts
│   └── GameEndScene.ts
├── utils/         # Utility classes
│   └── TicTapsConnector.ts
├── index.html     # Main HTML file
└── index.ts       # Game entry point
```

## Development

### Prerequisites

- Node.js (v14+)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install
```

### Running the Development Server

```bash
# Start development server
npm start
```

The game will be available at http://localhost:9000

### Building for Production

```bash
# Build optimized version
npm run build
```

The build output will be in the `dist` folder.

## TicTaps Integration

The game integrates with the TicTaps platform using the following messages:

- `notifyGameReady()` - Called when the game is loaded and ready to start
- `sendScore(score)` - Called when the game ends with the player's final score
- `notifyGameQuit()` - Called when the player quits or restarts the game

## Game Flow

1. **Preload Scene** - Loads all game assets
2. **Game Start Scene** - Shows title screen with start button and countdown
3. **Game Scene** - Main gameplay with timer, score, and matching mechanics
4. **Game End Scene** - Displays final score and restart button

