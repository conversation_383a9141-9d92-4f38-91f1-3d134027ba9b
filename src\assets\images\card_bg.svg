<svg width="448" height="448" viewBox="0 0 448 448" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ddd_816_14848)">
<rect x="16" y="16" width="400" height="400" rx="44" fill="url(#paint0_linear_816_14848)"/>
<rect x="18" y="18" width="396" height="396" rx="42" stroke="#F5F7FA" stroke-opacity="0.12" stroke-width="4"/>
</g>
<defs>
<filter id="filter0_ddd_816_14848" x="-92.7313" y="-92.7313" width="617.463" height="617.463" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0509804 0 0 0 0 0.0784314 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_816_14848"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.878431 0 0 0 0 0.878431 0 0 0 0 1 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_816_14848" result="effect2_dropShadow_816_14848"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0509804 0 0 0 0 0.0784314 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_816_14848" result="effect3_dropShadow_816_14848"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_816_14848" result="shape"/>
</filter>
<linearGradient id="paint0_linear_816_14848" x1="-184" y1="216" x2="216" y2="616" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5F7FA" stop-opacity="0.12"/>
<stop offset="0.521392" stop-color="#F5F7FA" stop-opacity="0.06"/>
<stop offset="1" stop-color="#F5F7FA" stop-opacity="0.01"/>
</linearGradient>
</defs>
</svg>
