{"name": "matching-mayhem-phaser", "version": "1.0.0", "description": "Matching Mayhem game ported to Phaser", "main": "src/index.ts", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production"}, "keywords": ["phaser", "game", "tictaps", "typescript", "matching", "mayhem"], "author": "", "license": "ISC", "dependencies": {"phaser": "^3.60.0"}, "devDependencies": {"@types/node": "^18.0.0", "copy-webpack-plugin": "^11.0.0", "fs-extra": "^11.3.0", "html-webpack-plugin": "^5.5.0", "sharp": "^0.34.1", "ts-loader": "^9.4.0", "typescript": "^5.0.0", "webpack": "^5.76.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}