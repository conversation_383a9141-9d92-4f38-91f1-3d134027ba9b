
// Animal and Color Types
export type AnimalName = 'eagle' | 'koala' | 'wolf' | 'dog';
export type ColorName = 'cyan' | 'green' | 'yellow';

export interface GameEndSceneData {
  score: number;
}

// TicTaps Platform Types
export interface TicTapsMessage {
  type: 'gameReady' | 'gameScore' | 'gameQuit';
  score?: number;
}

// Utility Types
export type Nullable<T> = T | null;


/**
 * Message types sent to TicTaps platform
 */
export type TicTapsMessageType = 'gameReady' | 'gameScore' | 'gameQuit';

/**
 * Message structure for TicTaps communication
 */
export interface TicTapsMessage {
  type: TicTapsMessageType;
  score?: number;
  data?: any;
}
