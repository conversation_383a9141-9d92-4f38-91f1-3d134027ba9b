# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Phaser 3 browser-based game called "Matching Mayhem" integrated with the TicTaps gaming platform. Players match images against four options, earning points based on speed and accuracy.

## Development Commands

```bash
# Install dependencies
npm install

# Start development server (runs on http://localhost:9000)
npm start

# Build for production (outputs to dist/)
npm run build
```

## Architecture

The game follows Phaser's scene-based architecture with these key components:

### Scene Flow
1. **PreloadScene** (`src/scenes/PreloadScene.ts`) - Loads all game assets with progress bar
2. **GameStartScene** (`src/scenes/GameStartScene.ts`) - Title screen with start button
3. **GameScene** (`src/scenes/GameScene.ts`) - Main gameplay logic with matching mechanics
4. **GameEndScene** (`src/scenes/GameEndScene.ts`) - Score display and restart option

### Key Components
- **MatchingCard** (`src/objects/MatchingCard.ts`) - Reusable card component for the matching game
- **TicTapsConnector** (`src/utils/TicTapsConnector.ts`) - Handles platform integration via postMessage API
- **TimerHelper** (`src/utils/TimerHelper.ts`) - Timer utilities for countdown and round timing

### Platform Integration
The game communicates with the TicTaps platform using postMessage:
- `notifyGameReady()` - Called when game is loaded
- `sendScore(score)` - Sends final score when game ends
- `notifyGameQuit()` - Called when player quits/restarts

### Game Configuration
- Canvas: 540x960px (portrait mobile)
- Background: #0E0F1E
- Physics: Arcade with no gravity
- Scaling: Auto-fit and center
- Multi-touch: 3 active pointers

## Asset Organization
```
src/assets/
├── images/    # Sprites, backgrounds, UI elements
├── sounds/    # Audio files (mp3, ogg, wav formats)
└── fonts/     # Bitmap and TTF fonts
```

## Technical Stack
- **Framework**: Phaser 3.60.0
- **Language**: TypeScript (ES2020 target)
- **Bundler**: Webpack with ts-loader
- **Image Processing**: Sharp library
- **Dev Server**: webpack-dev-server with HMR