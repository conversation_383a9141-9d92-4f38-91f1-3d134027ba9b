import 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';

// Wait for the DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
  // Check if the container exists
  const container = document.getElementById('game-container');
  if (!container) {
    console.error('Game container not found!');
    return;
  }

  // Game configuration
  const config: Phaser.Types.Core.GameConfig = {
    type: Phaser.AUTO,
    width: 540,
    height: 960,
    backgroundColor: '#0E0F1E',
    parent: 'game-container',
    scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { x: 0, y: 0 },
        debug: false
      }
    },
    scale: {
      mode: Phaser.Scale.EXPAND,
      autoCenter: Phaser.Scale.CENTER_BOTH,
    },
    render: {
      antialias: true,
      pixelArt: false,
      roundPixels: true,
      powerPreference: 'high-performance'
    },
    input: {
      activePointers: 3,
      windowEvents: false
    },
    dom: {
      createContainer: true
    }
  };

  // Create the game
  new Phaser.Game(config);
});
