// const KeyGen = {
//   seed: 0,
//   a: 1664525,
//   c: 1013904223,
//   m: 4294967296, // 2^32

//   setSeed: function (seed: number): void {
//     this.seed = seed;
//   },

//   next: function (): number {
//     this.seed = (this.a * this.seed + this.c) % this.m;
//     return this.seed;
//   },
// };

// KeyGen.setSeed(ttseed);
// console.log(KeyGen.next());


/**
 * Linear Congruential Generator (LCG)
 */
export default class SeededRandom {
  private seed: number;
  private readonly a = 1664525;    // Multiplier (from Numerical Recipes)
  private readonly c = 1013904223; // Increment
  private readonly m = 2 ** 32;    // Modulus (2^32)

  constructor(seed: number = Date.now()) {
    this.seed = seed >>> 0; // Ensure 32-bit unsigned integer
  }

  /**
   * Generate next random number between 0 and 1 (exclusive)
   */
  next(): number {
    this.seed = (this.a * this.seed + this.c) % this.m;
    return this.seed / this.m;
  }

  /**
   * Generate random integer between min and max (inclusive)
   */
  between(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  /**
   * Shuffle an array in place using Fisher-Yates algorithm
   */
  shuffle<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = this.between(0, i);
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  /**
   * Get current seed value
   */
  getSeed(): number {
    return this.seed;
  }

  /**
   * Reset with new seed
   */
  setSeed(newSeed: number): void {
    this.seed = newSeed >>> 0;
  }
}